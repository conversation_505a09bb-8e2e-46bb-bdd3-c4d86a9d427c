'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { ArrowLeft, Plus, Trash2 } from 'lucide-react';
import PageContainer from '@/components/layout/page-container';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  useGetOpportunitySection,
  useGetTalentSection,
  useGetDiscoverSection,
  useGetCompaniesSection,
  useGetPartnersSection,
  useGetCtaSection,
  useGetAppSection
} from '@/hooks/useQuery';
import {
  useUpdateOpportunitySection,
  useUpdateTalentSection,
  useUpdateDiscoverSection,
  useUpdateCompaniesSection,
  useUpdatePartnersSection,
  useUpdateCtaSection,
  useUpdateAppSection
} from '@/hooks/useMutation';

type SectionType =
  | 'opportunity'
  | 'talent'
  | 'discover'
  | 'companies'
  | 'partners'
  | 'cta'
  | 'app';

export default function EditSectionPage() {
  const params = useParams();
  const router = useRouter();
  const section = params.section as SectionType;

  const [formData, setFormData] = useState<any>({
    heading: '',
    description: '',
    image: '',
    subheading: '',
    appStoreURL: '',
    playStoreURL: '',
    steps: [],
    partners: []
  });

  // Get the appropriate hooks based on section type
  const getHookData = () => {
    switch (section) {
      case 'opportunity':
        return useGetOpportunitySection();
      case 'talent':
        return useGetTalentSection();
      case 'discover':
        return useGetDiscoverSection();
      case 'companies':
        return useGetCompaniesSection();
      case 'partners':
        return useGetPartnersSection();
      case 'cta':
        return useGetCtaSection();
      case 'app':
        return useGetAppSection();
      default:
        return { data: null, isLoading: false };
    }
  };

  const getMutationHook = () => {
    const onSuccess = () => {
      router.push('/dashboard/cms/home');
    };

    switch (section) {
      case 'opportunity':
        return useUpdateOpportunitySection({ onSuccess });
      case 'talent':
        return useUpdateTalentSection({ onSuccess });
      case 'discover':
        return useUpdateDiscoverSection({ onSuccess });
      case 'companies':
        return useUpdateCompaniesSection({ onSuccess });
      case 'partners':
        return useUpdatePartnersSection({ onSuccess });
      case 'cta':
        return useUpdateCtaSection({ onSuccess });
      case 'app':
        return useUpdateAppSection({ onSuccess });
      default:
        return { mutate: () => {}, isPending: false };
    }
  };

  const { data, isLoading } = getHookData();
  const { mutate: updateSection, isPending } = getMutationHook();
  const sectionData = data?.data?.section;

  const getSectionTitle = (type: SectionType) => {
    const titles = {
      opportunity: 'Opportunity Section',
      talent: 'Talent Section',
      discover: 'Discover Section',
      companies: 'Companies Section',
      partners: 'Partners Section',
      cta: 'Call to Action Section',
      app: 'Mobile App Section'
    };
    return titles[type];
  };

  useEffect(() => {
    if (sectionData) {
      setFormData({
        heading: sectionData.heading || '',
        description: sectionData.description || '',
        image: sectionData.image || '',
        subheading: sectionData.subheading || '',
        appStoreURL: sectionData.appStoreURL || '',
        playStoreURL: sectionData.playStoreURL || '',
        steps:
          sectionData.steps?.map((step: any) => ({
            heading: step.heading,
            description: step.description
          })) || [],
        partners:
          sectionData.partners?.map((partner: any) => ({
            name: partner.name,
            imageURL: partner.imageURL
          })) || []
      });
    }
  }, [sectionData]);

  const handleBack = () => {
    router.push('/dashboard/cms/home');
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev: any) => ({
      ...prev,
      [field]: value
    }));
  };

  const handleStepChange = (index: number, field: string, value: string) => {
    setFormData((prev: any) => ({
      ...prev,
      steps: prev.steps.map((step: any, i: number) =>
        i === index ? { ...step, [field]: value } : step
      )
    }));
  };

  const addStep = () => {
    setFormData((prev: any) => ({
      ...prev,
      steps: [...prev.steps, { heading: '', description: '' }]
    }));
  };

  const removeStep = (index: number) => {
    setFormData((prev: any) => ({
      ...prev,
      steps: prev.steps.filter((_: any, i: number) => i !== index)
    }));
  };

  const handlePartnerChange = (index: number, field: string, value: string) => {
    setFormData((prev: any) => ({
      ...prev,
      partners: prev.partners.map((partner: any, i: number) =>
        i === index ? { ...partner, [field]: value } : partner
      )
    }));
  };

  const addPartner = () => {
    setFormData((prev: any) => ({
      ...prev,
      partners: [...prev.partners, { name: '', imageURL: '' }]
    }));
  };

  const removePartner = (index: number) => {
    setFormData((prev: any) => ({
      ...prev,
      partners: prev.partners.filter((_: any, i: number) => i !== index)
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.heading.trim() || !formData.description.trim()) {
      return;
    }

    // Prepare data based on section type
    const submitData: any = {
      heading: formData.heading.trim(),
      description: formData.description.trim()
    };

    // Add optional fields if they exist
    if (formData.image?.trim()) {
      submitData.image = formData.image.trim();
    }

    if (formData.subheading?.trim()) {
      submitData.subheading = formData.subheading.trim();
    }

    // Add section-specific fields
    if (section === 'app') {
      if (formData.appStoreURL?.trim()) {
        submitData.appStoreURL = formData.appStoreURL.trim();
      }
      if (formData.playStoreURL?.trim()) {
        submitData.playStoreURL = formData.playStoreURL.trim();
      }
    }

    // Add steps if they exist
    if (formData.steps && formData.steps.length > 0) {
      const validSteps = formData.steps.filter(
        (step: any) => step.heading.trim() && step.description.trim()
      );
      if (validSteps.length > 0) {
        submitData.steps = validSteps;
      }
    }

    // Add partners if they exist
    if (formData.partners && formData.partners.length > 0) {
      const validPartners = formData.partners.filter((partner: any) =>
        partner.name.trim()
      );
      if (validPartners.length > 0) {
        submitData.partners = validPartners;
      }
    }

    updateSection(submitData);
  };

  if (isLoading) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col space-y-6'>
          <div className='flex items-center gap-4'>
            <Skeleton className='h-10 w-10' />
            <div className='space-y-2'>
              <Skeleton className='h-8 w-64' />
              <Skeleton className='h-4 w-48' />
            </div>
          </div>
          <div className='space-y-4'>
            <Skeleton className='h-32 w-full' />
            <Skeleton className='h-32 w-full' />
            <Skeleton className='h-32 w-full' />
          </div>
        </div>
      </PageContainer>
    );
  }

  if (!sectionData) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col space-y-6'>
          <div className='flex items-center gap-4'>
            <Button variant='ghost' size='sm' onClick={handleBack}>
              <ArrowLeft className='mr-2 h-4 w-4' />
              Back to CMS Home
            </Button>
          </div>
          <div className='py-12 text-center'>
            <h2 className='mb-2 text-2xl font-bold'>No Data Available</h2>
            <p className='text-muted-foreground'>
              This section doesn&apos;t have any data yet.
            </p>
          </div>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-6'>
        {/* Header */}
        <div className='flex items-center gap-4'>
          <Button variant='ghost' size='sm' onClick={handleBack}>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back to CMS Home
          </Button>
          <div>
            <h1 className='text-3xl font-bold'>
              Edit {getSectionTitle(section)}
            </h1>
            <p className='text-muted-foreground'>
              Update the content of this section
            </p>
          </div>
        </div>

        {/* Form */}
        <Card>
          <CardHeader>
            <CardTitle>Section Content</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className='space-y-6'>
              <div className='space-y-6'>
                {/* Basic Fields */}
                <div className='space-y-4'>
                  <div className='space-y-3'>
                    <Label htmlFor='heading'>Heading *</Label>
                    <Input
                      id='heading'
                      value={formData.heading}
                      onChange={(e) =>
                        handleInputChange('heading', e.target.value)
                      }
                      placeholder='Enter section heading'
                      required
                    />
                  </div>

                  <div className='space-y-3'>
                    <Label htmlFor='description'>Description *</Label>
                    <Textarea
                      id='description'
                      value={formData.description}
                      onChange={(e) =>
                        handleInputChange('description', e.target.value)
                      }
                      placeholder='Enter section description'
                      rows={3}
                      required
                    />
                  </div>

                  {/* Subheading for CTA section */}
                  {section === 'cta' && (
                    <div className='space-y-3'>
                      <Label htmlFor='subheading'>Subheading</Label>
                      <Input
                        id='subheading'
                        value={formData.subheading}
                        onChange={(e) =>
                          handleInputChange('subheading', e.target.value)
                        }
                        placeholder='Enter section subheading'
                      />
                    </div>
                  )}

                  <div className='space-y-3'>
                    <Label htmlFor='image'>Image URL</Label>
                    <Input
                      id='image'
                      value={formData.image}
                      onChange={(e) =>
                        handleInputChange('image', e.target.value)
                      }
                      placeholder='Enter image URL'
                      type='url'
                    />
                  </div>

                  {/* App Store URLs for app section */}
                  {section === 'app' && (
                    <>
                      <div className='space-y-3'>
                        <Label htmlFor='appStoreURL'>App Store URL</Label>
                        <Input
                          id='appStoreURL'
                          value={formData.appStoreURL}
                          onChange={(e) =>
                            handleInputChange('appStoreURL', e.target.value)
                          }
                          placeholder='Enter App Store URL'
                          type='url'
                        />
                      </div>
                      <div className='space-y-3'>
                        <Label htmlFor='playStoreURL'>Play Store URL</Label>
                        <Input
                          id='playStoreURL'
                          value={formData.playStoreURL}
                          onChange={(e) =>
                            handleInputChange('playStoreURL', e.target.value)
                          }
                          placeholder='Enter Play Store URL'
                          type='url'
                        />
                      </div>
                    </>
                  )}
                </div>

                {/* Steps Section - for sections that have steps */}
                {(section === 'opportunity' ||
                  section === 'talent' ||
                  section === 'discover') && (
                  <div>
                    <div className='mb-4 flex items-center justify-between'>
                      <Label className='text-base font-medium'>Steps</Label>
                      <Button
                        type='button'
                        variant='outline'
                        size='sm'
                        onClick={addStep}
                      >
                        <Plus className='mr-1 h-4 w-4' />
                        Add Step
                      </Button>
                    </div>

                    <div className='space-y-4'>
                      {formData.steps.map((step: any, index: number) => (
                        <Card
                          key={index}
                          className='border-l-primary border-l-4'
                        >
                          <CardHeader className='pb-3'>
                            <div className='flex items-center justify-between'>
                              <CardTitle className='text-sm'>
                                Step {index + 1}
                              </CardTitle>
                              <Button
                                type='button'
                                variant='ghost'
                                size='sm'
                                onClick={() => removeStep(index)}
                                className='text-destructive hover:text-destructive'
                              >
                                <Trash2 className='h-4 w-4' />
                              </Button>
                            </div>
                          </CardHeader>
                          <CardContent className='space-y-3'>
                            <div>
                              <Label htmlFor={`step-heading-${index}`}>
                                Step Heading
                              </Label>
                              <Input
                                id={`step-heading-${index}`}
                                value={step.heading}
                                onChange={(e) =>
                                  handleStepChange(
                                    index,
                                    'heading',
                                    e.target.value
                                  )
                                }
                                placeholder='Enter step heading'
                              />
                            </div>
                            <div>
                              <Label htmlFor={`step-description-${index}`}>
                                Step Description
                              </Label>
                              <Textarea
                                id={`step-description-${index}`}
                                value={step.description}
                                onChange={(e) =>
                                  handleStepChange(
                                    index,
                                    'description',
                                    e.target.value
                                  )
                                }
                                placeholder='Enter step description'
                                rows={2}
                              />
                            </div>
                          </CardContent>
                        </Card>
                      ))}

                      {formData.steps.length === 0 && (
                        <div className='text-muted-foreground py-8 text-center'>
                          <p>
                            No steps added yet. Click &quot;Add Step&quot; to
                            create your first step.
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Partners Section - for sections that have partners */}
                {(section === 'companies' || section === 'partners') && (
                  <div>
                    <div className='mb-4 flex items-center justify-between'>
                      <Label className='text-base font-medium'>Partners</Label>
                      <Button
                        type='button'
                        variant='outline'
                        size='sm'
                        onClick={addPartner}
                      >
                        <Plus className='mr-1 h-4 w-4' />
                        Add Partner
                      </Button>
                    </div>

                    <div className='space-y-4'>
                      {formData.partners.map((partner: any, index: number) => (
                        <Card
                          key={index}
                          className='border-l-primary border-l-4'
                        >
                          <CardHeader className='pb-3'>
                            <div className='flex items-center justify-between'>
                              <CardTitle className='text-sm'>
                                Partner {index + 1}
                              </CardTitle>
                              <Button
                                type='button'
                                variant='ghost'
                                size='sm'
                                onClick={() => removePartner(index)}
                                className='text-destructive hover:text-destructive'
                              >
                                <Trash2 className='h-4 w-4' />
                              </Button>
                            </div>
                          </CardHeader>
                          <CardContent className='space-y-3'>
                            <div>
                              <Label htmlFor={`partner-name-${index}`}>
                                Partner Name
                              </Label>
                              <Input
                                id={`partner-name-${index}`}
                                value={partner.name}
                                onChange={(e) =>
                                  handlePartnerChange(
                                    index,
                                    'name',
                                    e.target.value
                                  )
                                }
                                placeholder='Enter partner name'
                              />
                            </div>
                            <div>
                              <Label htmlFor={`partner-image-${index}`}>
                                Partner Image URL
                              </Label>
                              <Input
                                id={`partner-image-${index}`}
                                value={partner.imageURL}
                                onChange={(e) =>
                                  handlePartnerChange(
                                    index,
                                    'imageURL',
                                    e.target.value
                                  )
                                }
                                placeholder='Enter partner image URL'
                                type='url'
                              />
                            </div>
                          </CardContent>
                        </Card>
                      ))}

                      {formData.partners.length === 0 && (
                        <div className='text-muted-foreground py-8 text-center'>
                          <p>
                            No partners added yet. Click &quot;Add Partner&quot;
                            to create your first partner.
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Form Actions */}
              <div className='flex gap-4 border-t pt-4'>
                <Button
                  type='button'
                  variant='outline'
                  onClick={handleBack}
                  disabled={isPending}
                >
                  Cancel
                </Button>
                <Button
                  type='submit'
                  disabled={
                    isPending ||
                    !formData.heading.trim() ||
                    !formData.description.trim()
                  }
                >
                  {isPending ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </PageContainer>
  );
}
