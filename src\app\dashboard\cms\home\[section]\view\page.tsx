'use client';

import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { Arrow<PERSON>eft } from 'lucide-react';
import PageContainer from '@/components/layout/page-container';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import {
  useGetOpportunitySection,
  useGetTalentSection,
  useGetDiscoverSection,
  useGetCompaniesSection,
  useGetPartnersSection,
  useGetCtaSection,
  useGetAppSection
} from '@/hooks/useQuery';

type SectionType =
  | 'opportunity'
  | 'talent'
  | 'discover'
  | 'companies'
  | 'partners'
  | 'cta'
  | 'app';

export default function ViewSectionPage() {
  const params = useParams();
  const router = useRouter();
  const section = params.section as SectionType;

  // Get the appropriate hook based on section type
  const getHookData = () => {
    switch (section) {
      case 'opportunity':
        return useGetOpportunitySection();
      case 'talent':
        return useGetTalentSection();
      case 'discover':
        return useGetDiscoverSection();
      case 'companies':
        return useGetCompaniesSection();
      case 'partners':
        return useGetPartnersSection();
      case 'cta':
        return useGetCtaSection();
      case 'app':
        return useGetAppSection();
      default:
        return { data: null, isLoading: false };
    }
  };

  const { data, isLoading } = getHookData();
  const sectionData = data?.data?.section;

  const getSectionTitle = (type: SectionType) => {
    const titles = {
      opportunity: 'Opportunity Section',
      talent: 'Talent Section',
      discover: 'Discover Section',
      companies: 'Companies Section',
      partners: 'Partners Section',
      cta: 'Call to Action Section',
      app: 'Mobile App Section'
    };
    return titles[type];
  };

  const handleBack = () => {
    router.push('/dashboard/cms/home');
  };

  const handleEdit = () => {
    router.push(`/dashboard/cms/home/<USER>/edit`);
  };

  if (isLoading) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col space-y-6'>
          <div className='flex items-center gap-4'>
            <Skeleton className='h-10 w-10' />
            <div className='space-y-2'>
              <Skeleton className='h-8 w-64' />
              <Skeleton className='h-4 w-48' />
            </div>
          </div>
          <div className='space-y-4'>
            <Skeleton className='h-32 w-full' />
            <Skeleton className='h-32 w-full' />
            <Skeleton className='h-32 w-full' />
          </div>
        </div>
      </PageContainer>
    );
  }

  if (!sectionData) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col space-y-6'>
          <div className='flex items-center gap-4'>
            <Button variant='ghost' size='sm' onClick={handleBack}>
              <ArrowLeft className='mr-2 h-4 w-4' />
              Back to CMS Home
            </Button>
          </div>
          <div className='py-12 text-center'>
            <h2 className='mb-2 text-2xl font-bold'>No Data Available</h2>
            <p className='text-muted-foreground'>
              This section doesn&apos;t have any data yet.
            </p>
          </div>
        </div>
      </PageContainer>
    );
  }

  const renderBasicFields = () => (
    <div className='space-y-4'>
      <div>
        <h4 className='mb-2 text-sm font-medium'>Heading</h4>
        <p className='text-muted-foreground bg-muted rounded-md p-3 text-sm'>
          {sectionData.heading}
        </p>
      </div>
      <div>
        <h4 className='mb-2 text-sm font-medium'>Description</h4>
        <p className='text-muted-foreground bg-muted rounded-md p-3 text-sm'>
          {sectionData.description}
        </p>
      </div>
    </div>
  );

  const renderSteps = () => {
    if (!sectionData.steps || sectionData.steps.length === 0) return null;

    return (
      <div>
        <h4 className='mb-3 text-sm font-medium'>
          Steps ({sectionData.steps.length})
        </h4>
        <div className='space-y-3'>
          {sectionData.steps.map((step: any, index: number) => (
            <Card
              key={step._id || index}
              className='border-l-primary border-l-4'
            >
              <CardContent className='pt-4'>
                <div className='space-y-2'>
                  <div className='flex items-center gap-2'>
                    <Badge variant='outline'>Step {index + 1}</Badge>
                    <h5 className='font-medium'>{step.heading}</h5>
                  </div>
                  <p className='text-muted-foreground text-sm'>
                    {step.description}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  const renderPartners = () => {
    if (!sectionData.partners || sectionData.partners.length === 0) return null;

    return (
      <div>
        <h4 className='mb-3 text-sm font-medium'>
          Partners ({sectionData.partners.length})
        </h4>
        <div className='grid grid-cols-1 gap-3 sm:grid-cols-2'>
          {sectionData.partners.map((partner: any, index: number) => (
            <Card key={partner._id || index}>
              <CardContent className='pt-4'>
                <div className='space-y-2'>
                  <h5 className='font-medium'>{partner.name}</h5>
                  {partner.imageURL && (
                    <div className='text-muted-foreground text-sm'>
                      <span className='font-medium'>Image URL:</span>
                      <p className='bg-muted mt-1 rounded p-2 text-xs break-all'>
                        {partner.imageURL}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  const renderImage = () => {
    if (!sectionData.image) return null;

    return (
      <div>
        <h4 className='mb-2 text-sm font-medium'>Image</h4>
        <div className='bg-muted rounded-md p-3'>
          <p className='text-muted-foreground text-sm break-all'>
            {sectionData.image}
          </p>
        </div>
      </div>
    );
  };

  const renderSubheading = () => {
    if (!sectionData.subheading) return null;

    return (
      <div>
        <h4 className='mb-2 text-sm font-medium'>Subheading</h4>
        <p className='text-muted-foreground bg-muted rounded-md p-3 text-sm'>
          {sectionData.subheading}
        </p>
      </div>
    );
  };

  const renderAppUrls = () => {
    if (section !== 'app') return null;

    return (
      <div className='space-y-4'>
        <div>
          <h4 className='mb-2 text-sm font-medium'>App Store URL</h4>
          <div className='bg-muted rounded-md p-3'>
            <p className='text-muted-foreground text-sm break-all'>
              {sectionData.appStoreURL}
            </p>
          </div>
        </div>
        <div>
          <h4 className='mb-2 text-sm font-medium'>Play Store URL</h4>
          <div className='bg-muted rounded-md p-3'>
            <p className='text-muted-foreground text-sm break-all'>
              {sectionData.playStoreURL}
            </p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-6'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-4'>
            <Button variant='ghost' size='sm' onClick={handleBack}>
              <ArrowLeft className='mr-2 h-4 w-4' />
              Back to CMS Home
            </Button>
            <div>
              <div className='flex items-center gap-2'>
                <h1 className='text-3xl font-bold'>
                  {getSectionTitle(section)}
                </h1>
                <Badge variant='outline'>{section}</Badge>
              </div>
              <p className='text-muted-foreground'>
                View the current content of this section
              </p>
            </div>
          </div>
          <Button onClick={handleEdit}>Edit Section</Button>
        </div>

        {/* Content */}
        <Card>
          <CardHeader>
            <CardTitle>Section Content</CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className='max-h-[70vh] pr-4'>
              <div className='space-y-6'>
                {renderBasicFields()}

                {renderSubheading()}

                {renderImage()}

                {renderAppUrls()}

                {sectionData.steps && (
                  <>
                    <Separator />
                    {renderSteps()}
                  </>
                )}

                {sectionData.partners && (
                  <>
                    <Separator />
                    {renderPartners()}
                  </>
                )}

                <Separator />
                <div className='text-muted-foreground space-y-1 text-xs'>
                  <p>
                    <span className='font-medium'>ID:</span> {sectionData._id}
                  </p>
                  <p>
                    <span className='font-medium'>Version:</span>{' '}
                    {sectionData.__v}
                  </p>
                </div>
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      </div>
    </PageContainer>
  );
}
